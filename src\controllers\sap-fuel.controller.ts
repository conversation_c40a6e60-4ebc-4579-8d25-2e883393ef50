import {inject} from '@loopback/core';
import {
  Filter
} from '@loopback/repository';
import {get, param, post, requestBody, Response, RestBindings} from '@loopback/rest';
import {DateTime} from 'luxon';
import {Sap<PERSON><PERSON>, SapHazardous, SapNonHazardous} from '../models';
import {SapFuelRepository, SapHazardousRepository, SapNonHazardousRepository, SapResponseRepository, UserProfileRepository} from '../repositories';
import {SapService} from '../services/sap.service';

export class SapFuelController {
  constructor(
    @inject(RestBindings.Http.RESPONSE) private response: Response,
    @inject('services.S3Service') private s3Service: SapService,
    @inject('repositories.SapResponseRepository') private sapResponseRepository: SapResponseRepository,
    @inject('repositories.UserProfileRepository') private userProfileRepository: UserProfileRepository,

    @inject('repositories.SapFuelRepository') private fuelRepository: SapFuelRepository,
    @inject('repositories.SapHazardousRepository') private hazardRepository: SapHazardousRepository,
    @inject('repositories.SapNonHazardousRepository') private nonHazardRepository: SapNonHazardousRepository,
  ) { }
  @get('/get-s3-list')
  async getList(): Promise<any> {

    try {
      const bucketName = process.env.AWS_TVS_CLIENT_BUCKET_NAME;
      const folderName = 'Matrix/navigos/';


      if (!bucketName) {
        throw new Error('AWS_BUCKET_NAME is not defined in the environment variables');
      }


      const fileList = await this.s3Service.listData(bucketName, folderName);

      if (fileList.length > 0) {
        this.response.setHeader('Content-Type', 'application/json');
        this.response.send(fileList.filter((x: any) => x?.Key.includes('parquet')));
      } else {
        this.response.status(404).send('No files found in the folder.');
      }
    } catch (error) {
      console.error('Error in listFiles method:', error);
      this.response.status(500).send('Error listing files from S3');
    }
  }


  @post('/fetch-fuel-folders')
  async fetchFuelFolders(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              resetRepository: {type: 'boolean'}
            },

          },
        },
      },
    })
    requestBody: {

      resetRepository: boolean; // Nullable id
    },
  ): Promise<void> {
    try {
      const bucketName = process.env.AWS_TVS_CLIENT_BUCKET_NAME;
      const {resetRepository} = requestBody
      // Validate bucket name
      if (!bucketName) {
        console.error('Bucket name is missing.');
        throw new Error('Bucket name is required.');
      }

      const folderNames = ['Matrix/navigos/fuel_petrol', 'Matrix/navigos/fuel_except_petrol'];
      if (resetRepository) {

        // await this.sapResponseRepository.deleteAll({sapId: 'SAP1'})


      }
      // Fetch and process files from both folders
      for (const folderName of folderNames) {
        console.log(`Processing folder: ${folderName}`);
        await this.s3Service.storeFolder(bucketName, folderName, 5000, 1, resetRepository);
      }

      this.response.status(200).send(`All .parquet files from the specified folders fetched and saved successfully.`);

    } catch (error) {
      console.error('Error in fetchParquetFolders method:', error.message);
      this.response.status(500).send(`Error processing files from folders: ${error.message}`);
    }
  }
  @post('/fetch-hazard-folders')
  async fetchHazardFolders(@requestBody({
    content: {
      'application/json': {
        schema: {
          type: 'object',
          properties: {
            resetRepository: {type: 'boolean'}
          },

        },
      },
    },
  })
  requestBody: {

    resetRepository: boolean; // Nullable id
  },): Promise<void> {
    try {

      const bucketName = process.env.AWS_TVS_CLIENT_BUCKET_NAME;
      const {resetRepository} = requestBody
      // Validate bucket name
      if (!bucketName) {
        console.error('Bucket name is missing.');
        throw new Error('Bucket name is required.');
      }

      const folderNames = ['Matrix/navigos/discarded_container_waste', 'Matrix/navigos/ewaste', 'Matrix/navigos/lead_acid_batteries_waste', 'Matrix/navigos/lithium_ion_batteries_waste', 'Matrix/navigos/used_oil_waste', 'Matrix/navigos/waste_thinner'];
      if (resetRepository) {

        // await this.sapResponseRepository.deleteAll({sapId: 'SAP2'})


      }
      // Fetch and process files from both folders
      for (const folderName of folderNames) {
        console.log(`Processing hazard folder: ${folderName}`);
        await this.s3Service.storeFolder(bucketName, folderName, 5000, 2, resetRepository); // type: 2 for hazardous
      }

      this.response.status(200).send(`All .parquet files from the hazard folders fetched and saved successfully.`);

    } catch (error) {
      console.error('Error in fetchHazardFolders method:', error.message);
      this.response.status(500).send(`Error processing hazard files: ${error.message}`);
    }
  }

  @post('/fetch-nonhazard-folders')
  async fetchnonHazardFolders(@requestBody({
    content: {
      'application/json': {
        schema: {
          type: 'object',
          properties: {
            resetRepository: {type: 'boolean'}
          },

        },
      },
    },
  })
  requestBody: {

    resetRepository: boolean; // Nullable id
  },): Promise<void> {
    try {
      const bucketName = process.env.AWS_TVS_CLIENT_BUCKET_NAME;
      const {resetRepository} = requestBody
      // Validate bucket name
      if (!bucketName) {
        console.error('Bucket name is missing.');
        throw new Error('Bucket name is required.');
      }

      const folderNames = ['Matrix/navigos/cardboard_waste', 'Matrix/navigos/metal_waste', 'Matrix/navigos/packaging_waste', 'Matrix/navigos/paper_waste', 'Matrix/navigos/plastic_waste', 'Matrix/navigos/rubber_waste', 'Matrix/navigos/rubbish_waste', 'Matrix/navigos/scrap_waste', 'Matrix/navigos/thermocol_waste', 'Matrix/navigos/wood_waste'];
      if (resetRepository) {

        // await this.sapResponseRepository.deleteAll({sapId: 'SAP3'})


      }
      // Fetch and process files from both folders
      for (const folderName of folderNames) {
        console.log(`Processing hazard folder: ${folderName}`);
        await this.s3Service.storeFolder(bucketName, folderName, 5000, 3, resetRepository); // type: 2 for hazardous
      }

      this.response.status(200).send(`All .parquet files from the hazard folders fetched and saved successfully.`);

    } catch (error) {
      console.error('Error in fetchHazardFolders method:', error.message);
      this.response.status(500).send(`Error processing hazard files: ${error.message}`);
    }
  }
  @post('/fetch-purchase-folders')
  async fetchPurchaseAndGoodsFolders(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              resetRepository: {type: 'boolean'}
            },

          },
        },
      },
    })
    requestBody: {

      resetRepository: boolean; // Nullable id
    },
  ): Promise<void> {
    try {
      const bucketName = process.env.AWS_TVS_CLIENT_BUCKET_NAME;
      const {resetRepository} = requestBody
      // Validate bucket name
      if (!bucketName) {
        console.error('Bucket name is missing.');
        throw new Error('Bucket name is required.');
      }

      const folderNames = ['Matrix/navigos/Goods_and_Services/MONTH_ID=202504', 'Matrix/navigos/Goods_and_Services/MONTH_ID=202505'];
      if (resetRepository) {

        // await this.sapResponseRepository.deleteAll({sapId: 'SAP4'})


      }
      // await this.s3Service.PurchaseGoodsJSON_Direct()
      // Fetch and process files from both folders
      for (const folderName of folderNames) {

        await this.s3Service.storeFolder(bucketName, folderName, 5000, 4, resetRepository);
      }

      this.response.status(200).send(`All .parquet files from the specified folders fetched and saved successfully.`);

    } catch (error) {
      console.error('Error in fetchParquetFolders method:', error.message);
      this.response.status(500).send(`Error processing files from folders: ${error.message}`);
    }
  }
  @post('/fetch-capital-folders')
  async fetchCaptialGoodsFolders(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              resetRepository: {type: 'boolean'}
            },

          },
        },
      },
    })
    requestBody: {

      resetRepository: boolean; // Nullable id
    },
  ): Promise<void> {
    try {
      const bucketName = process.env.AWS_TVS_CLIENT_BUCKET_NAME;
      const {resetRepository} = requestBody
      // Validate bucket name
      if (!bucketName) {
        console.error('Bucket name is missing.');
        throw new Error('Bucket name is required.');
      }
      if (resetRepository) {

        // await this.sapResponseRepository.deleteAll({sapId: 'SAP5'})


      }
      const folderNames = ['Matrix/navigos/Capital_Goods/MONTH_ID=202504', 'Matrix/navigos/Capital_Goods/MONTH_ID=202505'];

      // Fetch and process files from both folders
      for (const folderName of folderNames) {
        console.log(`Processing folder: ${folderName}`);
        await this.s3Service.storeFolder(bucketName, folderName, 5000, 5, resetRepository);
      }

      this.response.status(200).send(`All .parquet files from the specified folders fetched and saved successfully.`);

    } catch (error) {
      console.error('Error in fetchParquetFolders method:', error.message);
      this.response.status(500).send(`Error processing files from folders: ${error.message}`);
    }
  }
  @post('/fetch-businesstravel-folders')
  async fetchBusinessTravelFolders(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              resetRepository: {type: 'boolean'}
            },

          },
        },
      },
    })
    requestBody: {

      resetRepository: boolean; // Nullable id
    },
  ): Promise<void> {
    try {
      const bucketName = process.env.AWS_TVS_CLIENT_BUCKET_NAME;
      const {resetRepository} = requestBody
      // Validate bucket name
      if (!bucketName) {
        console.error('Bucket name is missing.');
        throw new Error('Bucket name is required.');
      }

      const folderNames = ['Matrix/navigos/business_travel'];
      if (resetRepository) {

        // await this.sapResponseRepository.deleteAll({sapId: 'SAP6'})


      }
      // Fetch and process files from both folders
      for (const folderName of folderNames) {
        console.log(`Processing folder: ${folderName}`);
        await this.s3Service.storeFolder(bucketName, folderName, 5000, 6, resetRepository);
      }

      this.response.status(200).send(`All .parquet files from the specified folders fetched and saved successfully.`);

    } catch (error) {
      console.error('Error in fetchParquetFolders method:', error.message);
      this.response.status(500).send(`Error processing files from folders: ${error.message}`);
    }
  }
  @post('/fetch-downstream-folders')
  async fetchDownStreamFolders(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              resetRepository: {type: 'boolean'}
            },

          },
        },
      },
    })
    requestBody: {

      resetRepository: boolean; // Nullable id
    },
  ): Promise<void> {
    try {
      const bucketName = process.env.AWS_TVS_CLIENT_BUCKET_NAME;
      const {resetRepository} = requestBody
      // Validate bucket name
      if (!bucketName) {
        console.error('Bucket name is missing.');
        throw new Error('Bucket name is required.');
      }

      const folderNames = ['Matrix/navigos/downstream_transporation_Domestic', 'Matrix/navigos/downstream_transporation_Export', 'Matrix/navigos/downstream_transporation_Domestic/MONTH_ID=202504', 'Matrix/navigos/downstream_transporation_Domestic/MONTH_ID=202505', 'Matrix/navigos/downstream_transporation_Export/MONTH_ID=202504', 'Matrix/navigos/downstream_transporation_Export/MONTH_ID=202505'];

      if (resetRepository) {

        // await this.sapResponseRepository.deleteAll({sapId: 'SAP7'})


      }
      // Fetch and process files from both folders
      for (const folderName of folderNames) {
        console.log(`Processing folder: ${folderName}`);
        await this.s3Service.storeFolder(bucketName, folderName, 5000, 7, resetRepository);
      }

      this.response.status(200).send(`All .parquet files from the specified folders fetched and saved successfully.`);

    } catch (error) {
      console.error('Error in fetchParquetFolders method:', error.message);
      this.response.status(500).send(`Error processing files from folders: ${error.message}`);
    }
  }
  @post('/fetch-hremployee-folders')
  async fetchHREmployeeFolders(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              resetRepository: {type: 'boolean'}
            },

          },
        },
      },
    })
    requestBody: {

      resetRepository: boolean; // Nullable id
    },
  ): Promise<void> {
    try {
      const bucketName = process.env.AWS_TVS_CLIENT_BUCKET_NAME;
      const {resetRepository} = requestBody
      // Validate bucket name
      if (!bucketName) {
        console.error('Bucket name is missing.');
        throw new Error('Bucket name is required.');
      }

      const folderNames = ['Matrix/navigos/HR_Employee_Active_Data/202506', 'Matrix/navigos/HR_Employee_Inactive_Data'];
      if (resetRepository) {

        // await this.sapResponseRepository.deleteAll({sapId: 'SAP8'})


      }
      // Fetch and process files from both folders
      for (const folderName of folderNames) {
        console.log(`Processing folder: ${folderName}`);
        await this.s3Service.storeFolder(bucketName, folderName, 5000, 8, resetRepository);
      }

      this.response.status(200).send(`All .parquet files from the specified folders fetched and saved successfully.`);

    } catch (error) {
      console.error('Error in fetchParquetFolders method:', error.message);
      this.response.status(500).send(`Error processing files from folders: ${error.message}`);
    }
  }

  @post('/fetch-fuel-data')
  async fetchFuelData(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              startDate: {
                type: 'string',
              },
              endDate: {
                type: 'string',
              }

            },
            required: ['startDate', 'endDate'],
          }
        }
      },
    })
    requestBody: any,
    @param.filter(SapFuel) filter?: Filter<SapFuel>): Promise<any> {
    try {
      const {startDate, endDate} = requestBody
      const data = await this.fuelRepository.find(filter);

      const filteredData = await this.filterByDateRange(startDate, endDate, data);
      return {result: true, data: filteredData}
    } catch (e) {
      return {result: false}
    }
  }
  @post('/fetch-hazard-data')
  async fetchHazData(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              startDate: {
                type: 'string',
              },
              endDate: {
                type: 'string',
              }

            },
            required: ['startDate', 'endDate'],
          }
        }
      },
    })
    requestBody: any,
    @param.filter(SapHazardous) filter?: Filter<SapHazardous>): Promise<any> {
    try {
      const {startDate, endDate} = requestBody
      const data = await this.hazardRepository.find(filter);

      const filteredData = await this.filterByDateRange(startDate, endDate, data);
      return {result: true, data: filteredData}
    } catch (e) {
      return {result: false}
    }
  }


  @post('/fetch-nonhazard-data')
  async fetchnonHazData(
    @requestBody({
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              startDate: {
                type: 'string',
              },
              endDate: {
                type: 'string',
              }

            },
            required: ['startDate', 'endDate'],
          }
        }
      },
    })
    requestBody: any,
    @param.filter(SapNonHazardous) filter?: Filter<SapNonHazardous>): Promise<any> {
    try {
      const {startDate, endDate} = requestBody
      const data = await this.nonHazardRepository.find(filter);

      const filteredData = await this.filterByDateRange(startDate, endDate, data);
      return {result: true, data: filteredData}
    } catch (e) {
      return {result: false}
    }
  }
  async filterByDateRange(start: string, end: string, data: any[]) {
    // Convert start and end range to DateTime objects
    const startDate = DateTime.fromFormat(start, 'ddMyyyy')
    const endDate = DateTime.fromFormat(end, 'ddMyyyy')



    // Filter the data
    const filteredData = data.filter((item) => {

      if (item.Date) {
        const itemDate = DateTime.fromFormat(item.Date, 'yyyyMdd');

        return itemDate >= startDate && itemDate <= endDate;
      } else if (item.Date === null) {
        return true
      }
      return false;
    });

    return filteredData;
  }

}
