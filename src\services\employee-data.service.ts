import {inject} from '@loopback/core';
import {DateTime} from 'luxon';
import {<PERSON>rquetReader} from 'parquetjs-lite';
import {EmployeeData} from '../models';
import {EmployeeDataRepository} from '../repositories';

export class EmployeeDataService {
  constructor(
    @inject('repositories.EmployeeDataRepository') private employeeDataRepository: EmployeeDataRepository,
  ) { }

  // Function to map grade to EmployeeGrade based on the provided mapping table
  private mapGradeToEmployeeGrade(grade: string): string {
    if (!grade) return 'Non-Management';

    const gradeStr = grade.toString().toUpperCase().trim();

    // Senior Management: D8 Above (Equivalent Grades)
    const seniorManagementGrades = [
      'DA/SW1', 'A3',
      'DA/SW2', 'B1',
      'DA/SW3', 'B2',
      'DA/SW4', 'B3',
      'DA/SW5', 'C1',
      'DA/SW6', 'C2',
      'DA/SW7', 'C3',
      'DA/SW8', 'C3',
      'DA/SW9', 'D2',
      'DA/SW10', 'D4',
      'SS1', 'A1',
      'SS2', 'A2-A3',
      'SS3', 'B1-B2',
      'SS4', 'B3',
      'SS5', 'C1-C2',
      'SP1', 'C2',
      'SP2', 'D1',
      'SP3', 'D3'
    ];

    // Check for exact grade matches for Senior Management
    if (seniorManagementGrades.includes(gradeStr)) {
      return 'Senior Management';
    }

    // Check for DA/SW grades (D8 and above equivalent)
    if (gradeStr.startsWith('DA/SW')) {
      return 'Senior Management';
    }

    // Check for SS grades (Senior Staff)
    if (gradeStr.startsWith('SS')) {
      return 'Senior Management';
    }

    // Check for SP grades (Senior Professional)
    if (gradeStr.startsWith('SP')) {
      return 'Senior Management';
    }

    // Middle Management: Equivalent grades that are management but below D8
    const middleManagementGrades = [
      'D1', 'D2', 'D3', 'D4', 'D5', 'D6', 'D7',
      'C1', 'C2', 'C3',
      'B1', 'B2', 'B3',
      'A1', 'A2', 'A3'
    ];

    // Check for middle management equivalent grades
    if (middleManagementGrades.includes(gradeStr)) {
      return 'Middle Management';
    }

    // Non-Management: A1-B2 (Equivalent Grades)
    const nonManagementGrades = [
      'A1', 'A2', 'B1', 'B2'
    ];

    // Check for non-management grades
    if (nonManagementGrades.includes(gradeStr)) {
      return 'Non-Management';
    }

    // Default to Non-Management for any unrecognized grades
    return 'Non-Management';
  }

  // Function to determine EmployeeRoleType based on contract_type
  private getEmployeeRoleType(contractType: string): string {
    if (!contractType || contractType.trim() === '') return 'Worker';
    return contractType === '01_Permanent' ? 'Employee' : 'Worker';
  }

  // Function to determine EmployeeCategory based on employee_type
  private getEmployeeCategory(employeeType: string): string {
    if (!employeeType) return 'Other than Permanent';
    return employeeType.trim() === 'White Collar' ? 'Permanent' : 'Other than Permanent';
  }

  // Process raw employee data from parquet buffer for type === 8
  async processEmployeeDataFromBuffer(buffer: Buffer): Promise<{validRecords: EmployeeData[], totalCount: number, invalidCount: number, invalidReasons: string[]}> {
    const reader = await ParquetReader.openBuffer(buffer);
    const cursor = reader.getCursor();
    const employeeRecords: EmployeeData[] = [];
    const invalidReasons: string[] = [];
    let record;
    let totalCount = 0;
    let invalidCount = 0;

    try {
      while ((record = await cursor.next())) {
        totalCount++;

        // Store converted data based on business logic
        const employeeData: Partial<EmployeeData> = {
          employeeId: record.employee_id,
          EmployeeRoleType: this.getEmployeeRoleType(record.contract_type), // Converted: "Employee" or "Worker"
          EmployeeCategory: this.getEmployeeCategory(record.employee_type), // Converted: "Permanent" or "Other than Permanent"
          EmployeeGender: record.gender,
          EmployeeAge: record.age,
          EmployeeDOJ: record.date_of_joining,
          EmployeeDOE: record.date_of_exit || null,
          locationType: record.location_type,
          officeCity: record.office_city,
          officeLocation: record.office_location,
          raw_grade: record.grade, // Store raw grade
          EmployeeGrade: this.mapGradeToEmployeeGrade(record.grade), // Store converted grade classification
          employeeStatus: record.date_of_exit ? 'Inactive' : 'Active',
          syncDate: DateTime.utc().toString(),
          created_on: DateTime.utc().toString(),
          userProfileId: 289,
          locationId: 103,
        };

        // Enhanced validation with detailed reasons
        const validationErrors: string[] = [];


        if (!employeeData.EmployeeGender) {
          validationErrors.push('Missing gender');
        }
        if (!employeeData.EmployeeAge) {
          validationErrors.push('Missing age');
        }
        if (!employeeData.EmployeeDOJ) {
          validationErrors.push('Missing date_of_joining');
        }
        if (!employeeData.EmployeeCategory) {
          validationErrors.push('Missing EmployeeCategory');
        }
        if (!employeeData.EmployeeRoleType) {
          validationErrors.push('Missing EmployeeRoleType');
        }

        if (validationErrors.length === 0) {
          employeeRecords.push(new EmployeeData(employeeData));
        } else {
          invalidCount++;
          invalidReasons.push(`Record ${totalCount}: ${validationErrors.join(', ')}`);
        }
      }
    } catch (error) {
      console.error('Error processing employee data from parquet:', error);
      throw error;
    } finally {
      await reader.close();
    }

    return {
      validRecords: employeeRecords,
      totalCount,
      invalidCount,
      invalidReasons
    };
  }

  // Store employee data in batches
  async storeEmployeeData(employeeRecords: EmployeeData[], batchSize: number = 1000): Promise<void> {
    const batches = Math.ceil(employeeRecords.length / batchSize);

    for (let i = 0; i < batches; i++) {
      const batch = employeeRecords.slice(i * batchSize, (i + 1) * batchSize);

      try {
        await this.employeeDataRepository.createAll(batch);
        console.log(`Employee data batch ${i + 1} of ${batches} saved successfully.`);
      } catch (error) {
        console.error(`Error saving employee data batch ${i + 1}:`, error);
        throw error;
      }
    }
  }

  // Main method to process and store employee data for type === 8
  async processAndStoreEmployeeData(buffer: Buffer, batchSize: number = 1000, fileName?: string): Promise<void> {
    try {
      const fileInfo = fileName ? ` from file: ${fileName}` : '';
      const result = await this.processEmployeeDataFromBuffer(buffer);

      // Only log failed/rejected records
      if (result.invalidCount > 0) {
        console.log(`\n❌ VALIDATION FAILED RECORDS${fileInfo}:`);
        console.log(`   Total failed: ${result.invalidCount} out of ${result.totalCount} records`);
        result.invalidReasons.forEach((reason, index) => {
          console.log(`   ${index + 1}. ${reason}`);
        });
        console.log(''); // Empty line for separation
      }

      if (result.validRecords.length > 0) {
        await this.storeEmployeeData(result.validRecords, batchSize);
      }
    } catch (error) {
      const fileInfo = fileName ? ` from file: ${fileName}` : '';
      console.error(`❌ Error processing employee data${fileInfo}:`, error);
      throw error;
    }
  }

  // Get active employees
  async getActiveEmployees(): Promise<EmployeeData[]> {
    return this.employeeDataRepository.find({
      where: {employeeStatus: 'Active'}
    });
  }

  // Get inactive employees
  async getInactiveEmployees(): Promise<EmployeeData[]> {
    return this.employeeDataRepository.find({
      where: {employeeStatus: 'Inactive'}
    });
  }

  // Get employees by location
  async getEmployeesByLocation(locationId: number): Promise<EmployeeData[]> {
    return this.employeeDataRepository.find({
      where: {locationId}
    });
  }

  // Generate employee analytics data based on reporting period and date range
  async getEmployeeAnalytics(
    type: number, // 1-monthly, 2-Bi-Monthly, 3-Quarterly, 4-Annually, 5-Bi-Annually
    fromDate: string, // ddMyyyy format
    toDate: string // ddMyyyy format
  ): Promise<any[]> {
    try {
      // Parse dates
      const startDate = DateTime.fromFormat(fromDate, 'ddMMyyyy');
      const endDate = DateTime.fromFormat(toDate, 'ddMMyyyy');

      if (!startDate.isValid || !endDate.isValid) {
        throw new Error('Invalid date format. Use ddMMyyyy format.');
      }

      // Generate periods based on type
      const periods = this.generatePeriods(type, startDate, endDate);
      const analyticsData: any[] = [];

      // Process each period
      for (const period of periods) {
        const periodData = await this.calculatePeriodAnalytics(period);
        analyticsData.push(periodData);
      }

      return analyticsData;
    } catch (error) {
      console.error('Error generating employee analytics:', error);
      throw error;
    }
  }

  // Generate periods based on reporting type
  private generatePeriods(type: number, startDate: DateTime, endDate: DateTime): any[] {
    const periods: any[] = [];
    let currentDate = startDate.startOf('month');

    while (currentDate <= endDate) {
      let periodEnd: DateTime;
      let periodName: string;

      switch (type) {
        case 1: // Monthly
          periodEnd = currentDate.endOf('month');
          periodName = currentDate.toFormat('MMM yyyy');
          currentDate = currentDate.plus({months: 1});
          break;
        case 2: // Bi-Monthly
          periodEnd = currentDate.plus({months: 1}).endOf('month');
          periodName = `${currentDate.toFormat('MMM')}-${periodEnd.toFormat('MMM yyyy')}`;
          currentDate = currentDate.plus({months: 2});
          break;
        case 3: // Quarterly
          periodEnd = currentDate.plus({months: 2}).endOf('month');
          periodName = `Q${Math.ceil(currentDate.month / 3)} ${currentDate.year}`;
          currentDate = currentDate.plus({months: 3});
          break;
        case 4: // Annually
          periodEnd = currentDate.endOf('year');
          periodName = currentDate.toFormat('yyyy');
          currentDate = currentDate.plus({years: 1});
          break;
        case 5: // Bi-Annually
          periodEnd = currentDate.plus({months: 5}).endOf('month');
          periodName = `H${Math.ceil(currentDate.month / 6)} ${currentDate.year}`;
          currentDate = currentDate.plus({months: 6});
          break;
        default:
          throw new Error('Invalid type. Use 1-5 for different reporting periods.');
      }

      if (periodEnd > endDate) {
        periodEnd = endDate;
      }

      periods.push({
        periodName,
        startDate: currentDate.minus({months: type === 1 ? 1 : type === 2 ? 2 : type === 3 ? 3 : type === 4 ? 12 : 6}),
        endDate: periodEnd
      });

      if (currentDate > endDate) break;
    }

    return periods;
  }

  // Calculate analytics for a specific period
  private async calculatePeriodAnalytics(period: any): Promise<any> {
    const categories = this.getEmployeeCategories();
    const dataPoints: any[] = [];

    for (const category of categories) {
      // Calculate Total employees
      const totalCount = await this.calculateTotalEmployees(period, category);
      dataPoints.push({
        category: category.name,
        dataType: 'Total',
        count: totalCount,
        dpId: `DP${category.dpId}_TOTAL`,
        period: period.periodName
      });

      // Calculate New Employee Hire
      const newHireCount = await this.calculateNewHires(period, category);
      dataPoints.push({
        category: category.name,
        dataType: 'New Employee Hire',
        count: newHireCount,
        dpId: `DP${category.dpId}_HIRE`,
        period: period.periodName
      });

      // Calculate Employee Turnover
      const turnoverCount = await this.calculateTurnover(period, category);
      dataPoints.push({
        category: category.name,
        dataType: 'Employee Turnover',
        count: turnoverCount,
        dpId: `DP${category.dpId}_TURNOVER`,
        period: period.periodName
      });
    }

    return {
      period: period.periodName,
      startDate: period.startDate.toISODate(),
      endDate: period.endDate.toISODate(),
      dataPoints: dataPoints,
      totalDataPoints: dataPoints.length
    };
  }

  // Define exactly 20 employee categories as per your requirements
  private getEmployeeCategories(): any[] {
    return [
      // Employment Type by Gender (4 categories)
      {name: 'Permanent Male Employees', dpId: '001', filters: {EmployeeCategory: 'Permanent', EmployeeGender: 'Male'}},
      {name: 'Permanent Female Employees', dpId: '002', filters: {EmployeeCategory: 'Permanent', EmployeeGender: 'Female'}},
      {name: 'Other than Permanent Male Employees', dpId: '003', filters: {EmployeeCategory: 'Other than Permanent', EmployeeGender: 'Male'}},
      {name: 'Other than Permanent Female Employees', dpId: '004', filters: {EmployeeCategory: 'Other than Permanent', EmployeeGender: 'Female'}},

      // Senior Management by Gender and Age (6 categories)
      {name: 'Senior Management Male <30 years', dpId: '005', filters: {EmployeeGrade: 'Senior Management', EmployeeGender: 'Male', ageGroup: '<30'}},
      {name: 'Senior Management Female <30 years', dpId: '006', filters: {EmployeeGrade: 'Senior Management', EmployeeGender: 'Female', ageGroup: '<30'}},
      {name: 'Senior Management Male 30-50 Years', dpId: '007', filters: {EmployeeGrade: 'Senior Management', EmployeeGender: 'Male', ageGroup: '30-50'}},
      {name: 'Senior Management Female 30-50 Years', dpId: '008', filters: {EmployeeGrade: 'Senior Management', EmployeeGender: 'Female', ageGroup: '30-50'}},
      {name: 'Senior Management Male >50 years', dpId: '009', filters: {EmployeeGrade: 'Senior Management', EmployeeGender: 'Male', ageGroup: '>50'}},
      {name: 'Senior Management Female >50 years', dpId: '010', filters: {EmployeeGrade: 'Senior Management', EmployeeGender: 'Female', ageGroup: '>50'}},

      // Middle Management by Gender and Age (6 categories)
      {name: 'Middle Management Male <30 years', dpId: '011', filters: {EmployeeGrade: 'Middle Management', EmployeeGender: 'Male', ageGroup: '<30'}},
      {name: 'Middle Management Female <30 years', dpId: '012', filters: {EmployeeGrade: 'Middle Management', EmployeeGender: 'Female', ageGroup: '<30'}},
      {name: 'Middle Management Male 30-50 Years', dpId: '013', filters: {EmployeeGrade: 'Middle Management', EmployeeGender: 'Male', ageGroup: '30-50'}},
      {name: 'Middle Management Female 30-50 Years', dpId: '014', filters: {EmployeeGrade: 'Middle Management', EmployeeGender: 'Female', ageGroup: '30-50'}},
      {name: 'Middle Management Male >50 years', dpId: '015', filters: {EmployeeGrade: 'Middle Management', EmployeeGender: 'Male', ageGroup: '>50'}},
      {name: 'Middle Management Female >50 years', dpId: '016', filters: {EmployeeGrade: 'Middle Management', EmployeeGender: 'Female', ageGroup: '>50'}},

      // Worker Types by Gender and Employment Category (4 categories)
      {name: 'Permanent Male Workers', dpId: '017', filters: {EmployeeRoleType: 'Worker', EmployeeGender: 'Male', EmployeeCategory: 'Permanent'}},
      {name: 'Permanent Female Workers', dpId: '018', filters: {EmployeeRoleType: 'Worker', EmployeeGender: 'Female', EmployeeCategory: 'Permanent'}},
      {name: 'Other than Permanent Male Workers', dpId: '019', filters: {EmployeeRoleType: 'Worker', EmployeeGender: 'Male', EmployeeCategory: 'Other than Permanent'}},
      {name: 'Other than Permanent Female Workers', dpId: '020', filters: {EmployeeRoleType: 'Worker', EmployeeGender: 'Female', EmployeeCategory: 'Other than Permanent'}}
    ];
  }

  // Calculate total employees for a category in a period
  private async calculateTotalEmployees(period: any, category: any): Promise<number> {
    const whereClause: any = {
      // Employee must have joined before or during the period
      EmployeeDOJ: {lte: period.endDate.toISODate()},
      // AND either never left (null) OR left after the period
      or: [
        {EmployeeDOE: null},
        {EmployeeDOE: {gt: period.endDate.toISODate()}}
      ]
    };

    // Add category filters
    Object.keys(category.filters).forEach(key => {
      if (key === 'ageGroup') {
        // Handle age group filtering
        const ageGroup = category.filters[key];
        if (ageGroup === '<30') {
          whereClause.EmployeeAge = {lt: '30'};
        } else if (ageGroup === '30-50') {
          whereClause.EmployeeAge = {gte: '30', lte: '50'};
        } else if (ageGroup === '>50') {
          whereClause.EmployeeAge = {gt: '50'};
        }
      } else {
        whereClause[key] = category.filters[key];
      }
    });

    const result = await this.employeeDataRepository.count(whereClause);
    return result.count;
  }

  // Calculate new hires for a category in a period (only those who joined in this specific period)
  private async calculateNewHires(period: any, category: any): Promise<number> {
    const whereClause: any = {
      // Employee must have joined specifically within this period
      EmployeeDOJ: {
        gte: period.startDate.toISODate(),
        lte: period.endDate.toISODate()
      },
      // Ensure EmployeeDOJ is not null
      and: [
        {EmployeeDOJ: {neq: null}}
      ]
    };

    // Add category filters
    Object.keys(category.filters).forEach(key => {
      if (key === 'ageGroup') {
        // Handle age group filtering
        const ageGroup = category.filters[key];
        if (ageGroup === '<30') {
          whereClause.EmployeeAge = {lt: '30'};
        } else if (ageGroup === '30-50') {
          whereClause.EmployeeAge = {gte: '30', lte: '50'};
        } else if (ageGroup === '>50') {
          whereClause.EmployeeAge = {gt: '50'};
        }
      } else {
        whereClause[key] = category.filters[key];
      }
    });

    const result = await this.employeeDataRepository.count(whereClause);
    return result.count;
  }

  // Calculate employee turnover for a category in a period (only those who left in this specific period)
  private async calculateTurnover(period: any, category: any): Promise<number> {
    const whereClause: any = {
      // Employee must have left specifically within this period
      EmployeeDOE: {
        gte: period.startDate.toISODate(),
        lte: period.endDate.toISODate()
      },
      // Ensure EmployeeDOE is not null (they actually left)
      and: [
        {EmployeeDOE: {neq: null}},
        {employeeStatus: 'Inactive'}
      ]
    };

    // Add category filters
    Object.keys(category.filters).forEach(key => {
      if (key === 'ageGroup') {
        // Handle age group filtering
        const ageGroup = category.filters[key];
        if (ageGroup === '<30') {
          whereClause.EmployeeAge = {lt: '30'};
        } else if (ageGroup === '30-50') {
          whereClause.EmployeeAge = {gte: '30', lte: '50'};
        } else if (ageGroup === '>50') {
          whereClause.EmployeeAge = {gt: '50'};
        }
      } else {
        whereClause[key] = category.filters[key];
      }
    });

    const result = await this.employeeDataRepository.count(whereClause);
    return result.count;
  }
}
