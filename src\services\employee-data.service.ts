import {inject} from '@loopback/core';
import {DateTime} from 'luxon';
import {ParquetReader} from 'parquetjs-lite';
import {EmployeeData} from '../models';
import {EmployeeDataRepository} from '../repositories';

export class EmployeeDataService {
  constructor(
    @inject('repositories.EmployeeDataRepository') private employeeDataRepository: EmployeeDataRepository,
  ) { }

  // Function to map grade to EmployeeGrade based on your table
  private mapGradeToEmployeeGrade(grade: string, employeeCategory?: string, employeeRoleType?: string): string {
    // Only map grades for Permanent Employees, all others get "Other"
    if (employeeCategory !== 'Permanent' || employeeRoleType !== 'Employee') {
      return 'Other';
    }

    if (!grade) return 'Non-Management';

    // Clean the input grade - remove extra spaces and normalize dashes
    const inputGrade = grade.toString().toUpperCase().trim().replace(/\s*-\s*/g, '-');

    // Direct mapping based on your table
    const gradeToCategory: Record<string, string> = {
      // Senior Management grades from your table
      'D2': 'Senior Management',
      'D4': 'Senior Management',
      'D1': 'Senior Management',
      'D3': 'Senior Management',
      'DA/SW9': 'Senior Management',
      'DA/SW10': 'Senior Management',
      'SP2': 'Senior Management',
      'SP3': 'Senior Management',

      // Middle Management grades from your table
      'B3': 'Middle Management',
      'C1': 'Middle Management',
      'C2': 'Middle Management',
      'C3': 'Middle Management',
      'DA/SW4': 'Middle Management',
      'DA/SW5': 'Middle Management',
      'DA/SW6': 'Middle Management',
      'DA/SW7': 'Middle Management',
      'DA/SW8': 'Middle Management',
      'SS4': 'Middle Management',
      'SS5': 'Middle Management',
      'SP1': 'Middle Management',

      // Non-Management grades from your table
      'A3': 'Non-Management',
      'B1': 'Non-Management',
      'B2': 'Non-Management',
      'A1': 'Non-Management',
      'A2': 'Non-Management',
      'DA/SW1': 'Non-Management',
      'DA/SW2': 'Non-Management',
      'DA/SW3': 'Non-Management',
      'SS1': 'Non-Management',
      'SS2': 'Non-Management',
      'SS3': 'Non-Management',
    };

    // Check if grade exists in mapping
    const category = gradeToCategory[inputGrade];
    if (category) return category;

    // For any other D grade pattern (D followed by numbers) - Senior Management
    if (inputGrade.match(/^D\d+$/)) {
      return 'Senior Management';
    }

    // For Permanent Employees: If grade not found in mapping, default to Non-Management
    return 'Non-Management';
  }

  // Function to determine EmployeeRoleType based on contract_type
  private getEmployeeRoleType(contractType: string): string {
    if (!contractType || contractType.trim() === '') return 'Worker';
    return contractType === '01_Permanent' ? 'Employee' : 'Worker';
  }

  // Function to determine EmployeeCategory based on employee_type
  private getEmployeeCategory(employeeType: string): string {
    if (!employeeType) return 'Other than Permanent';
    return employeeType.trim() === 'White Collar' ? 'Permanent' : 'Other than Permanent';
  }

  // Process raw employee data from parquet buffer for type === 8
  async processEmployeeDataFromBuffer(buffer: Buffer): Promise<{validRecords: EmployeeData[], totalCount: number, invalidCount: number, invalidReasons: string[]}> {
    const reader = await ParquetReader.openBuffer(buffer);
    const cursor = reader.getCursor();
    const employeeRecords: EmployeeData[] = [];
    const invalidReasons: string[] = [];
    let record;
    let totalCount = 0;
    let invalidCount = 0;

    try {
      while ((record = await cursor.next())) {
        totalCount++;

        // Store converted data based on business logic
        const employeeRoleType = this.getEmployeeRoleType(record.contract_type);
        const employeeCategory = this.getEmployeeCategory(record.employee_type);

        const employeeData: Partial<EmployeeData> = {
          employeeId: record.employee_id,
          EmployeeRoleType: employeeRoleType, // Converted: "Employee" or "Worker"
          EmployeeCategory: employeeCategory, // Converted: "Permanent" or "Other than Permanent"
          EmployeeGender: record.gender,
          EmployeeAge: record.age,
          EmployeeDOJ: record.date_of_joining,
          EmployeeDOE: record.date_of_exit || null,
          locationType: record.location_type,
          officeCity: record.office_city,
          officeLocation: record.office_location,
          raw_grade: record.grade, // Store raw grade
          EmployeeGrade: this.mapGradeToEmployeeGrade(record.grade, employeeCategory, employeeRoleType), // Store converted grade classification
          employeeStatus: record.date_of_exit ? 'Inactive' : 'Active',
          syncDate: DateTime.utc().toString(),
          created_on: DateTime.utc().toString(),
          userProfileId: 289,
          locationId: 103,
        };

        // Enhanced validation with detailed reasons
        const validationErrors: string[] = [];


        if (!employeeData.EmployeeGender) {
          validationErrors.push('Missing gender');
        }
        if (!employeeData.EmployeeAge) {
          validationErrors.push('Missing age');
        }
        if (!employeeData.EmployeeDOJ) {
          validationErrors.push('Missing date_of_joining');
        }
        if (!employeeData.EmployeeCategory) {
          validationErrors.push('Missing EmployeeCategory');
        }
        if (!employeeData.EmployeeRoleType) {
          validationErrors.push('Missing EmployeeRoleType');
        }

        if (validationErrors.length === 0) {
          employeeRecords.push(new EmployeeData(employeeData));
        } else {
          invalidCount++;
          invalidReasons.push(`Record ${totalCount}: ${validationErrors.join(', ')}`);
        }
      }
    } catch (error) {
      console.error('Error processing employee data from parquet:', error);
      throw error;
    } finally {
      await reader.close();
    }

    return {
      validRecords: employeeRecords,
      totalCount,
      invalidCount,
      invalidReasons
    };
  }

  // Store employee data in batches
  async storeEmployeeData(employeeRecords: EmployeeData[], batchSize: number = 1000): Promise<void> {
    const batches = Math.ceil(employeeRecords.length / batchSize);

    for (let i = 0; i < batches; i++) {
      const batch = employeeRecords.slice(i * batchSize, (i + 1) * batchSize);

      try {
        await this.employeeDataRepository.createAll(batch);
        console.log(`Employee data batch ${i + 1} of ${batches} saved successfully.`);
      } catch (error) {
        console.error(`Error saving employee data batch ${i + 1}:`, error);
        throw error;
      }
    }
  }

  // Main method to process and store employee data for type === 8
  async processAndStoreEmployeeData(buffer: Buffer, batchSize: number = 1000, fileName?: string): Promise<void> {
    try {
      const fileInfo = fileName ? ` from file: ${fileName}` : '';
      const result = await this.processEmployeeDataFromBuffer(buffer);

      // Only log failed/rejected records
      if (result.invalidCount > 0) {
        console.log(`\n❌ VALIDATION FAILED RECORDS${fileInfo}:`);
        console.log(`   Total failed: ${result.invalidCount} out of ${result.totalCount} records`);
        result.invalidReasons.forEach((reason, index) => {
          console.log(`   ${index + 1}. ${reason}`);
        });
        console.log(''); // Empty line for separation
      }

      if (result.validRecords.length > 0) {
        await this.storeEmployeeData(result.validRecords, batchSize);
      }
    } catch (error) {
      const fileInfo = fileName ? ` from file: ${fileName}` : '';
      console.error(`❌ Error processing employee data${fileInfo}:`, error);
      throw error;
    }
  }

  // Get active employees
  async getActiveEmployees(): Promise<EmployeeData[]> {
    return this.employeeDataRepository.find({
      where: {employeeStatus: 'Active'}
    });
  }

  // Get inactive employees
  async getInactiveEmployees(): Promise<EmployeeData[]> {
    return this.employeeDataRepository.find({
      where: {employeeStatus: 'Inactive'}
    });
  }

  // Get employees by location
  async getEmployeesByLocation(locationId: number): Promise<EmployeeData[]> {
    return this.employeeDataRepository.find({
      where: {locationId}
    });
  }

  // Generate employee analytics data based on reporting period and date range
  async getEmployeeAnalytics(
    type: number, // 1-monthly, 2-Bi-Monthly, 3-Quarterly, 4-Annually, 5-Bi-Annually
    fromDate: string, // ddMyyyy format
    toDate: string // ddMyyyy format
  ): Promise<any[]> {
    try {
      // Parse dates
      const startDate = DateTime.fromFormat(fromDate, 'ddMMyyyy');
      const endDate = DateTime.fromFormat(toDate, 'ddMMyyyy');

      if (!startDate.isValid || !endDate.isValid) {
        throw new Error('Invalid date format. Use ddMMyyyy format.');
      }

      // Generate periods based on type
      const periods = this.generatePeriods(type, startDate, endDate);
      const analyticsData: any[] = [];

      // Process each period
      for (const period of periods) {
        const periodData = await this.calculatePeriodAnalytics(period);
        analyticsData.push(periodData);
      }

      return analyticsData;
    } catch (error) {
      console.error('Error generating employee analytics:', error);
      throw error;
    }
  }

  // Generate periods based on reporting type
  private generatePeriods(type: number, startDate: DateTime, endDate: DateTime): any[] {
    const periods: any[] = [];
    let currentDate = startDate.startOf('month');

    while (currentDate <= endDate) {
      let periodEnd: DateTime;
      let periodName: string;

      switch (type) {
        case 1: // Monthly
          periodEnd = currentDate.endOf('month');
          periodName = currentDate.toFormat('MMM yyyy');
          currentDate = currentDate.plus({months: 1});
          break;
        case 2: // Bi-Monthly
          periodEnd = currentDate.plus({months: 1}).endOf('month');
          periodName = `${currentDate.toFormat('MMM')}-${periodEnd.toFormat('MMM yyyy')}`;
          currentDate = currentDate.plus({months: 2});
          break;
        case 3: // Quarterly
          periodEnd = currentDate.plus({months: 2}).endOf('month');
          periodName = `Q${Math.ceil(currentDate.month / 3)} ${currentDate.year}`;
          currentDate = currentDate.plus({months: 3});
          break;
        case 4: // Annually
          periodEnd = currentDate.endOf('year');
          periodName = currentDate.toFormat('yyyy');
          currentDate = currentDate.plus({years: 1});
          break;
        case 5: // Bi-Annually
          periodEnd = currentDate.plus({months: 5}).endOf('month');
          periodName = `H${Math.ceil(currentDate.month / 6)} ${currentDate.year}`;
          currentDate = currentDate.plus({months: 6});
          break;
        default:
          throw new Error('Invalid type. Use 1-5 for different reporting periods.');
      }

      if (periodEnd > endDate) {
        periodEnd = endDate;
      }

      periods.push({
        periodName,
        startDate: currentDate.minus({months: type === 1 ? 1 : type === 2 ? 2 : type === 3 ? 3 : type === 4 ? 12 : 6}),
        endDate: periodEnd
      });

      if (currentDate > endDate) break;
    }

    return periods;
  }

  // Calculate analytics for a specific period
  private async calculatePeriodAnalytics(period: any): Promise<any> {
    const categories = this.getEmployeeCategories();
    const dataPoints: any[] = [];

    for (const category of categories) {
      // Calculate Total employees
      const totalCount = await this.calculateTotalEmployees(period, category);
      dataPoints.push({
        category: category.name,
        dataType: 'Total',
        count: totalCount,
        dpId: `DP${category.dpId}_TOTAL`,
        period: period.periodName
      });

      // Calculate New Employee Hire
      const newHireCount = await this.calculateNewHires(period, category);
      dataPoints.push({
        category: category.name,
        dataType: 'New Employee Hire',
        count: newHireCount,
        dpId: `DP${category.dpId}_HIRE`,
        period: period.periodName
      });

      // Calculate Employee Turnover
      const turnoverCount = await this.calculateTurnover(period, category);
      dataPoints.push({
        category: category.name,
        dataType: 'Employee Turnover',
        count: turnoverCount,
        dpId: `DP${category.dpId}_TURNOVER`,
        period: period.periodName
      });
    }

    return {
      period: period.periodName,
      startDate: period.startDate.toISODate(),
      endDate: period.endDate.toISODate(),
      dataPoints: dataPoints,
      totalDataPoints: dataPoints.length
    };
  }

  // Define exactly 26 employee categories as per your requirements
  private getEmployeeCategories(): any[] {
    return [
      // Employment Type by Gender (4 categories) - ALL employees regardless of role type
      {name: 'Permanent Male Employees', dpId: '001', filters: {EmployeeCategory: 'Permanent', EmployeeGender: 'Male'}},
      {name: 'Permanent Female Employees', dpId: '002', filters: {EmployeeCategory: 'Permanent', EmployeeGender: 'Female'}},
      {name: 'Other than Permanent Male Employees', dpId: '003', filters: {EmployeeCategory: 'Other than Permanent', EmployeeGender: 'Male'}},
      {name: 'Other than Permanent Female Employees', dpId: '004', filters: {EmployeeCategory: 'Other than Permanent', EmployeeGender: 'Female'}},

      // Senior Management by Gender and Age (6 categories) - Only Permanent Employees
      {name: 'Senior Management Male <30 years', dpId: '005', filters: {EmployeeGrade: 'Senior Management', EmployeeGender: 'Male', EmployeeCategory: 'Permanent', ageGroup: '<30'}},
      {name: 'Senior Management Female <30 years', dpId: '006', filters: {EmployeeGrade: 'Senior Management', EmployeeGender: 'Female', EmployeeCategory: 'Permanent', ageGroup: '<30'}},
      {name: 'Senior Management Male 30-50 Years', dpId: '007', filters: {EmployeeGrade: 'Senior Management', EmployeeGender: 'Male', EmployeeCategory: 'Permanent', ageGroup: '30-50'}},
      {name: 'Senior Management Female 30-50 Years', dpId: '008', filters: {EmployeeGrade: 'Senior Management', EmployeeGender: 'Female', EmployeeCategory: 'Permanent', ageGroup: '30-50'}},
      {name: 'Senior Management Male >50 years', dpId: '009', filters: {EmployeeGrade: 'Senior Management', EmployeeGender: 'Male', EmployeeCategory: 'Permanent', ageGroup: '>50'}},
      {name: 'Senior Management Female >50 years', dpId: '010', filters: {EmployeeGrade: 'Senior Management', EmployeeGender: 'Female', EmployeeCategory: 'Permanent', ageGroup: '>50'}},

      // Middle Management by Gender and Age (6 categories) - Only Permanent Employees
      {name: 'Middle Management Male <30 years', dpId: '011', filters: {EmployeeGrade: 'Middle Management', EmployeeGender: 'Male', EmployeeCategory: 'Permanent', ageGroup: '<30'}},
      {name: 'Middle Management Female <30 years', dpId: '012', filters: {EmployeeGrade: 'Middle Management', EmployeeGender: 'Female', EmployeeCategory: 'Permanent', ageGroup: '<30'}},
      {name: 'Middle Management Male 30-50 Years', dpId: '013', filters: {EmployeeGrade: 'Middle Management', EmployeeGender: 'Male', EmployeeCategory: 'Permanent', ageGroup: '30-50'}},
      {name: 'Middle Management Female 30-50 Years', dpId: '014', filters: {EmployeeGrade: 'Middle Management', EmployeeGender: 'Female', EmployeeCategory: 'Permanent', ageGroup: '30-50'}},
      {name: 'Middle Management Male >50 years', dpId: '015', filters: {EmployeeGrade: 'Middle Management', EmployeeGender: 'Male', EmployeeCategory: 'Permanent', ageGroup: '>50'}},
      {name: 'Middle Management Female >50 years', dpId: '016', filters: {EmployeeGrade: 'Middle Management', EmployeeGender: 'Female', EmployeeCategory: 'Permanent', ageGroup: '>50'}},

      // Non-Management by Gender and Age (6 categories) - Only Permanent Employees
      {name: 'Non-Management Male <30 years', dpId: '017', filters: {EmployeeGrade: 'Non-Management', EmployeeGender: 'Male', EmployeeCategory: 'Permanent', ageGroup: '<30'}},
      {name: 'Non-Management Female <30 years', dpId: '018', filters: {EmployeeGrade: 'Non-Management', EmployeeGender: 'Female', EmployeeCategory: 'Permanent', ageGroup: '<30'}},
      {name: 'Non-Management Male 30-50 Years', dpId: '019', filters: {EmployeeGrade: 'Non-Management', EmployeeGender: 'Male', EmployeeCategory: 'Permanent', ageGroup: '30-50'}},
      {name: 'Non-Management Female 30-50 Years', dpId: '020', filters: {EmployeeGrade: 'Non-Management', EmployeeGender: 'Female', EmployeeCategory: 'Permanent', ageGroup: '30-50'}},
      {name: 'Non-Management Male >50 years', dpId: '021', filters: {EmployeeGrade: 'Non-Management', EmployeeGender: 'Male', EmployeeCategory: 'Permanent', ageGroup: '>50'}},
      {name: 'Non-Management Female >50 years', dpId: '022', filters: {EmployeeGrade: 'Non-Management', EmployeeGender: 'Female', EmployeeCategory: 'Permanent', ageGroup: '>50'}},

      // Worker Types by Gender and Employment Category (4 categories) - ONLY Workers
      {name: 'Permanent Male Workers', dpId: '023', filters: {EmployeeRoleType: 'Worker', EmployeeGender: 'Male', EmployeeCategory: 'Permanent'}},
      {name: 'Permanent Female Workers', dpId: '024', filters: {EmployeeRoleType: 'Worker', EmployeeGender: 'Female', EmployeeCategory: 'Permanent'}},
      {name: 'Other than Permanent Male Workers', dpId: '025', filters: {EmployeeRoleType: 'Worker', EmployeeGender: 'Male', EmployeeCategory: 'Other than Permanent'}},
      {name: 'Other than Permanent Female Workers', dpId: '026', filters: {EmployeeRoleType: 'Worker', EmployeeGender: 'Female', EmployeeCategory: 'Other than Permanent'}}
    ];
  }

  // Calculate total employees for a category in a period
  private async calculateTotalEmployees(period: any, category: any): Promise<number> {
    // Use raw SQL query for proper date and filter handling
    let sql = `
      SELECT COUNT(*) as count
      FROM EmployeeData
      WHERE 1=1
    `;
    const params: any[] = [];

    // Add category filters first
    Object.keys(category.filters).forEach(key => {
      if (key === 'ageGroup') {
        // Handle age group filtering - EmployeeAge is stored as string but treat as number
        const ageGroup = category.filters[key];
        if (ageGroup === '<30') {
          sql += ` AND CAST(EmployeeAge AS INTEGER) < 30`;
        } else if (ageGroup === '30-50') {
          sql += ` AND CAST(EmployeeAge AS INTEGER) >= 30 AND CAST(EmployeeAge AS INTEGER) <= 50`;
        } else if (ageGroup === '>50') {
          sql += ` AND CAST(EmployeeAge AS INTEGER) > 50`;
        }
      } else {
        // Apply all other filters including EmployeeCategory: 'Permanent'
        sql += ` AND ${key} = ?`;
        params.push(category.filters[key]);
      }
    });

    // Add date filtering - convert DD-MM-YYYY to comparable format
    const periodEndDate = period.endDate.toFormat('yyyy-MM-dd');
    sql += ` AND STR_TO_DATE(EmployeeDOJ, '%d-%m-%Y') <= STR_TO_DATE(?, '%Y-%m-%d')`;
    params.push(periodEndDate);

    sql += ` AND (EmployeeDOE IS NULL OR STR_TO_DATE(EmployeeDOE, '%d-%m-%Y') > STR_TO_DATE(?, '%Y-%m-%d'))`;
    params.push(periodEndDate);

    const result = await this.employeeDataRepository.execute(sql, params);
    return result[0]?.count || 0;
  }

  // Calculate new hires for a category in a period (only those who joined in this specific period)
  private async calculateNewHires(period: any, category: any): Promise<number> {
    // Use raw SQL query for proper date and filter handling
    let sql = `
      SELECT COUNT(*) as count
      FROM EmployeeData
      WHERE EmployeeDOJ IS NOT NULL
    `;
    const params: any[] = [];

    // Add category filters first
    Object.keys(category.filters).forEach(key => {
      if (key === 'ageGroup') {
        // Handle age group filtering - EmployeeAge is stored as string but treat as number
        const ageGroup = category.filters[key];
        if (ageGroup === '<30') {
          sql += ` AND CAST(EmployeeAge AS INTEGER) < 30`;
        } else if (ageGroup === '30-50') {
          sql += ` AND CAST(EmployeeAge AS INTEGER) >= 30 AND CAST(EmployeeAge AS INTEGER) <= 50`;
        } else if (ageGroup === '>50') {
          sql += ` AND CAST(EmployeeAge AS INTEGER) > 50`;
        }
      } else {
        // Apply all other filters including EmployeeCategory: 'Permanent'
        sql += ` AND ${key} = ?`;
        params.push(category.filters[key]);
      }
    });

    // Add date filtering for new hires - joined within this period
    const periodStartDate = period.startDate.toFormat('yyyy-MM-dd');
    const periodEndDate = period.endDate.toFormat('yyyy-MM-dd');

    sql += ` AND STR_TO_DATE(EmployeeDOJ, '%d-%m-%Y') >= STR_TO_DATE(?, '%Y-%m-%d')`;
    params.push(periodStartDate);

    sql += ` AND STR_TO_DATE(EmployeeDOJ, '%d-%m-%Y') <= STR_TO_DATE(?, '%Y-%m-%d')`;
    params.push(periodEndDate);

    const result = await this.employeeDataRepository.execute(sql, params);
    return result[0]?.count || 0;
  }

  // Calculate employee turnover for a category in a period (only those who left in this specific period)
  private async calculateTurnover(period: any, category: any): Promise<number> {
    // Use raw SQL query for proper date and filter handling
    let sql = `
      SELECT COUNT(*) as count
      FROM EmployeeData
      WHERE EmployeeDOE IS NOT NULL
      AND employeeStatus = 'Inactive'
    `;
    const params: any[] = [];

    // Add category filters first
    Object.keys(category.filters).forEach(key => {
      if (key === 'ageGroup') {
        // Handle age group filtering - EmployeeAge is stored as string but treat as number
        const ageGroup = category.filters[key];
        if (ageGroup === '<30') {
          sql += ` AND CAST(EmployeeAge AS INTEGER) < 30`;
        } else if (ageGroup === '30-50') {
          sql += ` AND CAST(EmployeeAge AS INTEGER) >= 30 AND CAST(EmployeeAge AS INTEGER) <= 50`;
        } else if (ageGroup === '>50') {
          sql += ` AND CAST(EmployeeAge AS INTEGER) > 50`;
        }
      } else {
        // Apply all other filters including EmployeeCategory: 'Permanent'
        sql += ` AND ${key} = ?`;
        params.push(category.filters[key]);
      }
    });

    // Add date filtering for turnover - left within this period
    const periodStartDate = period.startDate.toFormat('yyyy-MM-dd');
    const periodEndDate = period.endDate.toFormat('yyyy-MM-dd');

    sql += ` AND STR_TO_DATE(EmployeeDOE, '%d-%m-%Y') >= STR_TO_DATE(?, '%Y-%m-%d')`;
    params.push(periodStartDate);

    sql += ` AND STR_TO_DATE(EmployeeDOE, '%d-%m-%Y') <= STR_TO_DATE(?, '%Y-%m-%d')`;
    params.push(periodEndDate);

    const result = await this.employeeDataRepository.execute(sql, params);
    return result[0]?.count || 0;
  }

  // Reprocess all employee grades with updated mapping logic
  async reprocessAllGrades(): Promise<{message: string, updated: number}> {
    try {
      // Get all employees
      const allEmployees = await this.employeeDataRepository.find();
      let updatedCount = 0;

      console.log(`Starting grade reprocessing for ${allEmployees.length} employees...`);

      // Process in batches to avoid memory issues
      const batchSize = 100;
      for (let i = 0; i < allEmployees.length; i += batchSize) {
        const batch = allEmployees.slice(i, i + batchSize);

        for (const employee of batch) {
          // Recalculate the grade using the updated mapping
          const newGrade = this.mapGradeToEmployeeGrade(
            employee.raw_grade || '',
            employee.EmployeeCategory,
            employee.EmployeeRoleType
          );

          // Only update if the grade has changed
          if (employee.EmployeeGrade !== newGrade) {
            await this.employeeDataRepository.updateById(employee.id, {
              EmployeeGrade: newGrade
            });
            updatedCount++;
          }
        }

        console.log(`Processed batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(allEmployees.length / batchSize)}`);
      }

      console.log(`Grade reprocessing completed. Updated ${updatedCount} employees.`);

      return {
        message: `Successfully reprocessed grades for ${allEmployees.length} employees. Updated ${updatedCount} records.`,
        updated: updatedCount
      };
    } catch (error) {
      console.error('Error reprocessing grades:', error);
      throw error;
    }
  }

  // Test grade mapping for debugging
  async testGradeMapping(grade: string): Promise<{inputGrade: string, mappedGrade: string}> {
    const mappedGrade = this.mapGradeToEmployeeGrade(grade, 'Permanent', 'Employee');
    return {
      inputGrade: grade,
      mappedGrade: mappedGrade
    };
  }
}
